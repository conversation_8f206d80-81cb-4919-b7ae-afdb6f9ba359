import React from 'react';
import { Tabs, <PERSON>lex, Button } from 'antd';
import { CheckCircleOutlined, ClockCircleOutlined, WarningOutlined, CalendarOutlined } from '@ant-design/icons';
import { Card } from '../../card/card';
import styles from './cs-spot.module.scss';

const tabItems = [
  {
    key: 'internal',
    label: (
      <span
        style={{
          width: '100%',
          display: 'inline-block',
          textAlign: 'center',
        }}>
        Internal Briefs
      </span>
    ),
    children: (
      <>
        <Flex className={styles.cardsRow} gap={24} justify="center" style={{ width: '100%' }}>
          <Card className={`${styles.cardItem} spacing-py-8 spacing-px-16`}>
            <Flex align="center" gap={16} className={styles.cardContent}>
              <div className={styles.iconBlueContainer}>
                <CheckCircleOutlined className={`${styles.cardIcon} ${styles.iconBlue}`} />
              </div>
              <div>
                <div className={styles.cardNumber}>9</div>
                <div className={styles.cardTitle}>Total Briefs</div>
                <div className={styles.cardSubtitle}>All active briefs</div>
              </div>
            </Flex>
          </Card>
          <Card className={`${styles.cardItem} spacing-py-8 spacing-px-16`}>
            <Flex align="center" gap={16} className={styles.cardContent}>
              <div className={styles.iconGreenContainer}>
                <ClockCircleOutlined className={`${styles.cardIcon} ${styles.iconGreen}`} />
              </div>
              <div>
                <div className={styles.cardNumber}>0</div>
                <div className={styles.cardTitle}>New Today</div>
                <div className={styles.cardSubtitle}>Created in last 24hrs</div>
              </div>
            </Flex>
          </Card>
          <Card className={`${styles.cardItem} spacing-py-8 spacing-px-16`}>
            <Flex align="center" gap={16} className={styles.cardContent}>
              <div className={styles.iconYellowContainer}>
                <WarningOutlined className={`${styles.cardIcon} ${styles.iconYellow}`} />
              </div>
              <div>
                <div className={styles.cardNumber}>4</div>
                <div className={styles.cardTitle}>Urgent</div>
                <div className={styles.cardSubtitle}>Expiring within 7 days</div>
              </div>
            </Flex>
          </Card>
          <Card className={`${styles.cardItem} spacing-py-8 spacing-px-16`}>
            <Flex align="center" gap={16} className={styles.cardContent}>
              <div className={styles.iconYellowContainer}>
                <CalendarOutlined className={`${styles.cardIcon} ${styles.iconRed}`} />
              </div>
              <div>
                <div className={styles.cardNumber}>4</div>
                <div className={styles.cardTitle}>Expiring Soon</div>
                <div className={styles.cardSubtitle}>Expiring within 3 days</div>
              </div>
            </Flex>
          </Card>
        </Flex>
        <div className={styles.briefsSection}>
          <div className={styles.briefsSearchBar}>
            <Flex gap={16} align="center" className={styles.briefsSearchBar}>
              <input
                className={styles.searchInput}
                type="text"
                placeholder="Search briefs, tags, content, or try natural language..."
              />
              <Flex gap={12} className={styles.searchActions}>
                <Button color="default" variant="outlined">
                  <CheckCircleOutlined style={{ fontSize: 18, color: '#2b6ef7' }} />
                  All Types
                </Button>
                <Button color="default" variant="outlined">
                  <CalendarOutlined style={{ fontSize: 18, color: '#2b6ef7' }} />
                  Last 60 days
                </Button>
                <Button danger type="primary">
                  + New Brief
                </Button>
              </Flex>
            </Flex>
          </div>
          <Flex justify="center" vertical gap={16} className={styles.briefsEmptyState}>
            <div className={styles.emptyText}>No briefs match your filters</div>
            <div>
              <Button danger type="primary" className={styles.createBriefBtn}>
                + Create First Brief
              </Button>
            </div>
          </Flex>
        </div>
      </>
    ),
  },
  {
    key: 'family',
    label: (
      <span
        style={{
          width: '100%',
          display: 'inline-block',
          textAlign: 'center',
        }}>
        Family Retention & Experience Audits
      </span>
    ),
    children: <div />,
  },
];

const CSSpot = () => {
  return (
    <div className={styles.container}>
      <h1 className="heading-1 spacing-my-16">Schola Platform</h1>
      <div style={{ width: '100%' }}>
        <Tabs
          items={tabItems}
          tabBarStyle={{
            display: 'flex',
            width: '100%',
          }}
          moreIcon={null}
          style={{ width: '100%' }}
        />
      </div>
    </div>
  );
};

export default CSSpot;
